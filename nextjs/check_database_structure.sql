-- Check current database structure before making changes
-- This script will help us understand what exists and what needs to be created

-- PART 1: CHECK EXISTING TABLES
-- =============================

-- Check if payouts table exists
SELECT 'PAYOUTS TABLE EXISTS:' as section;
SELECT EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_name = 'payouts'
) as payouts_table_exists;

-- Check payouts table structure if it exists
SELECT 'PAYOUTS TABLE STRUCTURE:' as section;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'payouts'
ORDER BY ordinal_position;

-- Check if payout_status enum exists
SELECT 'PAYOUT_STATUS ENUM EXISTS:' as section;
SELECT EXISTS (
    SELECT 1 FROM pg_type WHERE typname = 'payout_status'
) as payout_status_enum_exists;

-- Check enum values if it exists
SELECT 'PAYOUT_STATUS ENUM VALUES:' as section;
SELECT enumlabel as enum_value
FROM pg_enum
WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'payout_status')
ORDER BY enumsortorder;

-- PART 2: CHECK OTHER RELATED TABLES
-- ==================================

-- Check orders table
SELECT 'ORDERS TABLE EXISTS:' as section;
SELECT EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_name = 'orders'
) as orders_table_exists;

-- Check order_items table
SELECT 'ORDER_ITEMS TABLE EXISTS:' as section;
SELECT EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_name = 'order_items'
) as order_items_table_exists;

-- Check stores table
SELECT 'STORES TABLE EXISTS:' as section;
SELECT EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_name = 'stores'
) as stores_table_exists;

-- Check profiles table
SELECT 'PROFILES TABLE EXISTS:' as section;
SELECT EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_name = 'profiles'
) as profiles_table_exists;

-- PART 3: CHECK SAMPLE DATA
-- =========================

-- Check if there are any existing payouts
SELECT 'EXISTING PAYOUTS COUNT:' as section;
SELECT COUNT(*) as payout_count FROM payouts;

-- Show sample payouts if any exist
SELECT 'SAMPLE PAYOUTS:' as section;
SELECT id, store_id, order_id, amount, currency, payout_status, created_at
FROM payouts
ORDER BY created_at DESC
LIMIT 3;

-- Check existing orders
SELECT 'EXISTING ORDERS COUNT:' as section;
SELECT COUNT(*) as order_count FROM orders;

-- Show sample orders
SELECT 'SAMPLE ORDERS:' as section;
SELECT id, user_id, status, total, currency, created_at
FROM orders
ORDER BY created_at DESC
LIMIT 3;

-- PART 4: CHECK CURRENT USER ROLES
-- ================================

-- Check user roles
SELECT 'USER ROLES DISTRIBUTION:' as section;
SELECT role, COUNT(*) as count
FROM profiles
GROUP BY role
ORDER BY count DESC;

-- Check specific user
SELECT '<EMAIL> DETAILS:' as section;
SELECT id, email, role, first_name, last_name, created_at
FROM profiles
WHERE email = '<EMAIL>';

-- PART 5: RECOMMENDATIONS
-- =======================

SELECT 'RECOMMENDATIONS:' as section;
SELECT 
    CASE 
        WHEN NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'payouts') 
        THEN 'CREATE payouts table first'
        WHEN NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'payout_status')
        THEN 'CREATE payout_status enum type'
        WHEN NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'payouts' AND column_name = 'commission')
        THEN 'ADD commission column to payouts table'
        ELSE 'Database structure looks good, proceed with enhancements'
    END as recommendation;
