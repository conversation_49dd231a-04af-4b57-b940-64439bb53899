"use client";
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AdminHeader, DataTable, StatusBadge } from '@/components/admin';
import { getStore, getStoreStats, getStoreProducts } from '@/features/admin/api';
import { AdminStore, AdminProduct, ProductListParams } from '@/features/admin/types';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Edit, Eye, Package, ShoppingCart, DollarSign, Star, StarOff, Phone, Mail, MapPin } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, AlertCircle } from 'lucide-react';
import Image from 'next/image';

export default function StoreDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const [store, setStore] = useState<AdminStore | null>(null);
  const [stats, setStats] = useState<{
    products_count: number;
    orders_count: number;
    total_sales: number;
    recent_orders: any[];
  } | null>(null);
  const [products, setProducts] = useState<AdminProduct[]>([]);
  const [productsCount, setProductsCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [storeId, setStoreId] = useState<string | null>(null);
  const [productParams, setProductParams] = useState<ProductListParams>({
    page: 1,
    per_page: 5,
    sort_by: 'created_at',
    sort_order: 'desc',
  });

  // Unwrap params using React.use()
  useEffect(() => {
    const unwrapParams = async () => {
      const resolvedParams = await params;
      setStoreId(resolvedParams.id);
    };
    unwrapParams();
  }, [params]);

  useEffect(() => {
    if (!storeId) return;

    const fetchStoreData = async () => {
      setLoading(true);
      try {
        // Fetch store details, stats, and products in parallel
        const [storeData, statsData, productsData] = await Promise.all([
          getStore(storeId),
          getStoreStats(storeId),
          getStoreProducts(storeId, productParams)
        ]);

        if (storeData) {
          setStore(storeData);
          setStats(statsData);
          setProducts(productsData.products);
          setProductsCount(productsData.count);
        } else {
          setError('Store not found');
        }
      } catch (error) {
        console.error('Error fetching store data:', error);
        setError('Failed to load store data');
      } finally {
        setLoading(false);
      }
    };

    fetchStoreData();
  }, [storeId, productParams]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error || !store) {
    return (
      <div className="space-y-6">
        <AdminHeader
          title="Store Not Found"
          description="The requested store could not be found"
          backHref="/admin/stores"
        />
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {error || 'The store you are trying to view does not exist or you don\'t have permission to view it.'}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GM', {
      style: 'currency',
      currency: 'GMD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Product table columns
  const productColumns = [
    {
      key: 'name',
      header: 'Product',
      cell: (product: AdminProduct) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
            <Package className="h-5 w-5 text-gray-500" />
          </div>
          <div>
            <div className="font-medium">{product.name}</div>
            <div className="text-sm text-gray-500">{product.category_name}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'price',
      header: 'Price',
      cell: (product: AdminProduct) => (
        <div className="font-medium">
          {formatCurrency(product.price)}
        </div>
      ),
    },
    {
      key: 'status',
      header: 'Status',
      cell: (product: AdminProduct) => (
        <div className="flex items-center gap-2">
          <StatusBadge status={product.in_stock ? 'active' : 'disabled'} />
          {product.featured && <Star className="h-4 w-4 text-yellow-500" />}
        </div>
      ),
    },
    {
      key: 'actions',
      header: 'Actions',
      cell: (product: AdminProduct) => (
        <div className="flex items-center gap-2">
          <Link href={`/admin/products/${product.id}`}>
            <Button variant="ghost" size="sm">
              <Eye className="h-4 w-4" />
            </Button>
          </Link>
          <Link href={`/admin/products/${product.id}/edit`}>
            <Button variant="ghost" size="sm">
              <Edit className="h-4 w-4" />
            </Button>
          </Link>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <AdminHeader
        title={store.name}
        description="Store details and management"
        backHref="/admin/stores"
        actionHref={`/admin/stores/${store.id}/edit`}
        actionLabel="Edit Store"
      />

      {/* Store Header */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-start gap-6">
            {/* Store Logo */}
            <div className="w-24 h-24 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
              {store.logo ? (
                <Image
                  src={store.logo}
                  alt={store.name}
                  width={96}
                  height={96}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="text-2xl font-bold text-gray-500">
                  {store.name.charAt(0).toUpperCase()}
                </div>
              )}
            </div>

            {/* Store Info */}
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-2xl font-bold">{store.name}</h1>
                <StatusBadge status={store.status} />
                {store.featured && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <Star className="h-3 w-3" />
                    Featured
                  </Badge>
                )}
              </div>

              {store.description && (
                <p className="text-gray-600 mb-4">{store.description}</p>
              )}

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                {store.contact_email && (
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-gray-500" />
                    <span>{store.contact_email}</span>
                  </div>
                )}
                {store.contact_phone && (
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-gray-500" />
                    <span>{store.contact_phone}</span>
                  </div>
                )}
                {store.address && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    <span>{store.address}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Products</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.products_count}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.orders_count}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Sales</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(stats.total_sales)}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Recent Products */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Recent Products</CardTitle>
            <CardDescription>Latest products from this store</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Link href={`/admin/products?store_id=${store.id}`}>
              <Button variant="outline" size="sm">
                View All Products
              </Button>
            </Link>
            <Link href={`/admin/products/new?store_id=${store.id}`}>
              <Button size="sm">
                Add Product
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          {products.length > 0 ? (
            <DataTable
              data={products}
              columns={productColumns}
              searchable={false}
              pagination={false}
            />
          ) : (
            <div className="text-center py-8 text-gray-500">
              No products found for this store
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Orders */}
      {stats && stats.recent_orders.length > 0 && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Recent Orders</CardTitle>
              <CardDescription>Latest orders containing products from this store</CardDescription>
            </div>
            <Link href={`/admin/orders?store_id=${store.id}`}>
              <Button variant="outline" size="sm">
                View All Orders
              </Button>
            </Link>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats.recent_orders.map((order: any) => (
                <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <div>
                      <div className="font-medium">Order #{order.id.slice(0, 8)}</div>
                      <div className="text-sm text-gray-500">
                        {order.shipping_name} • {order.shipping_email}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <div className="font-medium">{formatCurrency(order.total)}</div>
                      <div className="text-sm text-gray-500">{formatDate(order.created_at)}</div>
                    </div>
                    <StatusBadge status={order.status} />
                    <Link href={`/admin/orders/${order.id}`}>
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Store Owner Info */}
      <Card>
        <CardHeader>
          <CardTitle>Store Owner</CardTitle>
          <CardDescription>Owner information and contact details</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div>
              <span className="font-medium">Email: </span>
              <span>{store.owner_email || 'Not available'}</span>
            </div>
            <div>
              <span className="font-medium">Owner ID: </span>
              <span className="font-mono text-sm">{store.owner_id}</span>
            </div>
            <div>
              <span className="font-medium">Store Created: </span>
              <span>{formatDate(store.created_at)}</span>
            </div>
            <div>
              <span className="font-medium">Last Updated: </span>
              <span>{formatDate(store.updated_at)}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
