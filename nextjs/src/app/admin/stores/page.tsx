"use client";
import React, { useState, useEffect } from 'react';
import { AdminHeader, DataTable, StatusBadge } from '@/components/admin';
import CSVUpload from '@/components/admin/CSVUpload';
import { getStores } from '@/features/admin/api';
import { AdminStore, StoreListParams, StoreStatus } from '@/features/admin/types';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Edit, Eye, Star, StarOff, Upload } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import Image from 'next/image';

export default function StoresPage() {
  const [stores, setStores] = useState<AdminStore[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [showCSVUpload, setShowCSVUpload] = useState(false);
  const [params, setParams] = useState<StoreListParams>({
    page: 1,
    per_page: 10,
    sort_by: 'created_at',
    sort_order: 'desc',
  });

  useEffect(() => {
    const fetchStores = async () => {
      setLoading(true);
      try {
        const { stores, count } = await getStores(params);
        setStores(stores);
        setTotalCount(count);
      } catch (error) {
        console.error('Error fetching stores:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStores();
  }, [params]);

  const handlePageChange = (page: number) => {
    setParams({ ...params, page });
  };

  const handleSearch = (search: string) => {
    setParams({ ...params, search, page: 1 });
  };

  const handleSortChange = (key: string, order: 'asc' | 'desc') => {
    setParams({ ...params, sort_by: key, sort_order: order, page: 1 });
  };

  const handleStatusFilter = (status: StoreStatus | 'all') => {
    if (status === 'all') {
      const { status, ...rest } = params;
      setParams({ ...rest, page: 1 });
    } else {
      setParams({ ...params, status, page: 1 });
    }
  };

  const handleFeaturedFilter = (featured: boolean | 'all') => {
    if (featured === 'all') {
      const { featured, ...rest } = params;
      setParams({ ...rest, page: 1 });
    } else {
      setParams({ ...params, featured, page: 1 });
    }
  };

  const columns = [
    {
      key: 'name',
      header: 'Store',
      cell: (store: AdminStore) => (
        <div className="flex items-center gap-3">
          {store.logo ? (
            <div className="w-10 h-10 rounded-full overflow-hidden">
              <Image
                src={store.logo}
                alt={store.name}
                width={40}
                height={40}
                className="object-cover"
              />
            </div>
          ) : (
            <div className="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center">
              <span className="text-primary-700 font-medium">
                {store.name.substring(0, 2).toUpperCase()}
              </span>
            </div>
          )}
          <div>
            <div className="font-medium">{store.name}</div>
            <div className="text-sm text-gray-500">{store.slug}</div>
          </div>
        </div>
      ),
      sortable: true,
    },
    {
      key: 'owner',
      header: 'Owner',
      cell: (store: AdminStore) => <div>{store.owner_email || '-'}</div>,
    },
    {
      key: 'status',
      header: 'Status',
      cell: (store: AdminStore) => <StatusBadge status={store.status} />,
      sortable: true,
    },
    {
      key: 'featured',
      header: 'Featured',
      cell: (store: AdminStore) => (
        <div>
          {store.featured ? (
            <Star className="h-5 w-5 text-amber-500 fill-amber-500" />
          ) : (
            <StarOff className="h-5 w-5 text-gray-300" />
          )}
        </div>
      ),
      sortable: true,
    },
    {
      key: 'created_at',
      header: 'Created',
      cell: (store: AdminStore) => (
        <div>{new Date(store.created_at).toLocaleDateString()}</div>
      ),
      sortable: true,
    },
    {
      key: 'actions',
      header: 'Actions',
      cell: (store: AdminStore) => (
        <div className="flex items-center gap-2">
          <Link href={`/admin/stores/${store.id}`}>
            <Button variant="ghost" size="sm">
              <Eye className="h-4 w-4" />
            </Button>
          </Link>
          <Link href={`/admin/stores/${store.id}/edit`}>
            <Button variant="ghost" size="sm">
              <Edit className="h-4 w-4" />
            </Button>
          </Link>
        </div>
      ),
    },
  ];

  const handleCSVUploadComplete = (result: any) => {
    if (result.success > 0) {
      // Refresh the stores list
      const fetchStores = async () => {
        try {
          const { stores, count } = await getStores(params);
          setStores(stores);
          setTotalCount(count);
        } catch (error) {
          console.error('Error refreshing stores:', error);
        }
      };
      fetchStores();
    }
    setShowCSVUpload(false);
  };

  return (
    <div className="space-y-6">
      <AdminHeader
        title="Stores"
        description="Manage your marketplace stores"
        actionHref="/admin/stores/new"
        actionLabel="Add Store"
      />

      {/* CSV Upload Section */}
      {showCSVUpload && (
        <CSVUpload
          type="stores"
          onUploadComplete={handleCSVUploadComplete}
        />
      )}

      {/* Action Buttons */}
      <div className="flex gap-2">
        <Button
          variant="outline"
          onClick={() => setShowCSVUpload(!showCSVUpload)}
        >
          <Upload className="h-4 w-4 mr-2" />
          {showCSVUpload ? 'Hide' : 'Show'} CSV Upload
        </Button>
      </div>

      <div className="flex flex-wrap items-center gap-4 mb-4">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Status:</span>
          <Select
            value={params.status || 'all'}
            onValueChange={(value) => handleStatusFilter(value as StoreStatus | 'all')}
          >
            <SelectTrigger className="w-32">
              <SelectValue placeholder="All Statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="disabled">Disabled</SelectItem>
              <SelectItem value="suspended">Suspended</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Featured:</span>
          <Select
            value={params.featured !== undefined ? params.featured.toString() : 'all'}
            onValueChange={(value) => handleFeaturedFilter(value === 'all' ? 'all' : value === 'true')}
          >
            <SelectTrigger className="w-32">
              <SelectValue placeholder="All" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="true">Featured</SelectItem>
              <SelectItem value="false">Not Featured</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <DataTable
        columns={columns}
        data={stores}
        totalCount={totalCount}
        pageSize={params.per_page || 10}
        currentPage={params.page || 1}
        onPageChange={handlePageChange}
        onSearch={handleSearch}
        searchPlaceholder="Search stores..."
        onSortChange={handleSortChange}
        sortKey={params.sort_by}
        sortOrder={params.sort_order}
        isLoading={loading}
      />
    </div>
  );
}
