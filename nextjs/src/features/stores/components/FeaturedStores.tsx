'use client';

import React from 'react';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import { StoreGrid } from './StoreGrid';
import { useFeaturedStores } from '../queries';

interface FeaturedStoresProps {
  title: string;
  subtitle?: string;
  limit?: number;
  className?: string;
  variant?: 'default' | 'compact' | 'featured';
}

export function FeaturedStores({
  title,
  subtitle,
  limit = 4,
  className = '',
  variant = 'featured',
}: FeaturedStoresProps) {
  console.log('FeaturedStores component rendering with props:', { title, limit, variant });

  const { data, isLoading, error } = useFeaturedStores(limit);

  console.log('useFeaturedStores hook result:', { data, isLoading, error });

  const stores = data?.stores || [];

  console.log('FeaturedStores component:', {
    isLoading,
    error,
    storesCount: stores.length,
    stores,
    storesData: JSON.stringify(stores)
  });

  return (
    <section className={`py-12 ${className}`}>
      <div className="container mx-auto px-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div className="flex-1">
            <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
            {subtitle && <p className="mt-1 text-gray-500">{subtitle}</p>}
          </div>
          <Link
            href="/stores"
            className="mt-4 sm:mt-0 inline-flex items-center text-primary-600 hover:text-primary-700 font-medium transition-colors duration-200 self-start sm:self-center"
          >
            View all
            <ArrowRight className="ml-1 h-4 w-4" />
          </Link>
        </div>

        {isLoading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6 animate-pulse">
            {[...Array(limit)].map((_, index) => (
              <div key={index} className="bg-gray-200 rounded-lg aspect-[16/9]"></div>
            ))}
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <p className="text-red-500">Error loading stores</p>
          </div>
        ) : (
          <StoreGrid
            stores={stores}
            variant={variant}
            columns={variant === 'compact' ? 5 : 4}
          />
        )}
      </div>
    </section>
  );
}
