-- Debug and Fix Store Orders Issue
-- This script will diagnose and fix the store orders problem

-- PART 1: DIAGNOSIS
-- =================

-- 1. Check the current user's profile and role
SELECT 'USER PROFILES:' as section;
SELECT id, email, role, first_name, last_name, created_at
FROM profiles
WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')
ORDER BY email;

-- 2. Check stores and their owners
SELECT 'STORES:' as section;
SELECT s.id, s.name, s.owner_id, p.email as owner_email, s.status, s.created_at
FROM stores s
LEFT JOIN profiles p ON s.owner_id = p.id
ORDER BY s.created_at DESC;

-- 3. Check all orders in the system
SELECT 'ALL ORDERS:' as section;
SELECT o.id, o.user_id, p.email as user_email, o.status, o.total, o.currency, o.created_at
FROM orders o
LEFT JOIN profiles p ON o.user_id = p.id
ORDER BY o.created_at DESC
LIMIT 10;

-- 4. Check order_items and their store relationships
SELECT 'ORDER ITEMS:' as section;
SELECT oi.id, oi.order_id, oi.product_id, oi.store_id, oi.quantity, oi.price, oi.total,
       s.name as store_name, p.name as product_name, st.email as store_owner_email
FROM order_items oi
LEFT JOIN stores s ON oi.store_id = s.id
LEFT JOIN products p ON oi.product_id = p.id
LEFT JOIN profiles st ON s.owner_id = st.id
ORDER BY oi.created_at DESC
LIMIT 10;

-- 5. Check <NAME_EMAIL>'s stores
SELECT '<EMAIL> STORES:' as section;
SELECT s.id, s.name, s.owner_id, s.status, s.created_at
FROM stores s
JOIN profiles p ON s.owner_id = p.id
WHERE p.email = '<EMAIL>';

-- 6. Summary counts
SELECT 'SUMMARY COUNTS:' as section;
SELECT
  (SELECT COUNT(*) FROM profiles WHERE role = 'store_owner') as store_owners_count,
  (SELECT COUNT(*) FROM stores) as stores_count,
  (SELECT COUNT(*) FROM orders) as orders_count,
  (SELECT COUNT(*) FROM order_items) as order_items_count,
  (SELECT COUNT(*) FROM products) as products_count;

-- PART 2: FIX THE ISSUE
-- =====================

-- The issue <NAME_EMAIL> is a store_owner but has no stores
-- Let's fix this by either:
-- 1. Assigning existing <NAME_EMAIL>, OR
-- 2. Creating a test <NAME_EMAIL>

-- First, let's see if there are any stores without owners or with invalid owners
SELECT 'STORES WITHOUT VALID OWNERS:' as section;
SELECT s.id, s.name, s.owner_id, s.status
FROM stores s
LEFT JOIN profiles p ON s.owner_id = p.id
WHERE p.id IS NULL OR p.role != 'store_owner';

-- Option 1: Assign existing <NAME_EMAIL> (if any exist without proper owners)
-- Uncomment the following lines if you want to assign existing stores:

/*
UPDATE stores
SET owner_id = (SELECT id FROM profiles WHERE email = '<EMAIL>')
WHERE owner_id IS NULL OR owner_id NOT IN (SELECT id FROM profiles WHERE role = 'store_owner')
RETURNING id, name, owner_id;
*/

-- Option 2: Create a test <NAME_EMAIL>
-- Uncomment the following lines to create a test store:

/*
INSERT INTO stores (
  id,
  name,
  description,
  owner_id,
  status,
  featured,
  created_at,
  updated_at
) VALUES (
  gen_random_uuid(),
  'Test <NAME_EMAIL>',
  'A test store for debugging store owner orders',
  (SELECT id FROM profiles WHERE email = '<EMAIL>'),
  'active',
  false,
  NOW(),
  NOW()
) RETURNING id, name, owner_id;
*/
