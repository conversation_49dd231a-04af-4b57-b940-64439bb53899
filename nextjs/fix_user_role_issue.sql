-- Fix user role and store association issue
-- This script will diagnose and <NAME_EMAIL> role/store issue

-- PART 1: DIAGNOSIS
-- =================

-- 1. Check <EMAIL> profile and role
SELECT '<EMAIL> PROFILE:' as section;
SELECT id, email, role, first_name, last_name, created_at
FROM profiles
WHERE email = '<EMAIL>';

-- 2. <NAME_EMAIL> has any stores
SELECT '<EMAIL> STORES:' as section;
SELECT s.id, s.name, s.owner_id, s.status, s.created_at
FROM stores s
JOIN profiles p ON s.owner_id = p.id
WHERE p.email = '<EMAIL>';

-- 3. Check all orders in the system
SELECT 'ALL ORDERS:' as section;
SELECT o.id, o.user_id, p.email as user_email, o.status, o.total, o.currency, o.created_at
FROM orders o
LEFT JOIN profiles p ON o.user_id = p.id
ORDER BY o.created_at DESC
LIMIT 5;

-- 4. Check if there are any <NAME_EMAIL>
SELECT '<NAME_EMAIL>:' as section;
SELECT o.id, o.user_id, o.status, o.total, o.currency, o.created_at
FROM orders o
JOIN profiles p ON o.user_id = p.id
WHERE p.email = '<EMAIL>'
ORDER BY o.created_at DESC;

-- 5. Check all stores and their owners
SELECT 'ALL STORES:' as section;
SELECT s.id, s.name, s.owner_id, p.email as owner_email, s.status, s.created_at
FROM stores s
LEFT JOIN profiles p ON s.owner_id = p.id
ORDER BY s.created_at DESC;

-- PART 2: FIX THE ISSUE
-- =====================

-- Option 1: Change <EMAIL> role to 'user' (if they shouldn't be a store owner)
-- Uncomment the following <NAME_EMAIL> should be a regular user:

-- UPDATE profiles SET role = 'user' WHERE email = '<EMAIL>';

-- Option 2: Create a <NAME_EMAIL> (if they should be a store owner)
-- Uncomment the following <NAME_EMAIL> should have a store:

/*
INSERT INTO stores (
  id,
  name,
  slug,
  description,
  owner_id,
  status,
  featured,
  created_at,
  updated_at
) VALUES (
  gen_random_uuid(),
  'User Store',
  'user-store',
  '<NAME_EMAIL>',
  (SELECT id FROM profiles WHERE email = '<EMAIL>'),
  'active',
  false,
  NOW(),
  NOW()
) RETURNING id, name, slug, owner_id;
*/

-- Option 3: Assign an existing <NAME_EMAIL>
-- First, let's see if there are stores without proper owners:

SELECT 'STORES WITHOUT VALID OWNERS:' as section;
SELECT s.id, s.name, s.owner_id, s.status
FROM stores s
LEFT JOIN profiles p ON s.owner_id = p.id
WHERE p.id IS NULL OR p.role != 'store_owner';

-- Uncomment the following to assign the first available <NAME_EMAIL>:
/*
UPDATE stores
SET owner_id = (SELECT id FROM profiles WHERE email = '<EMAIL>')
WHERE id = (
  SELECT s.id FROM stores s
  LEFT JOIN profiles p ON s.owner_id = p.id
  WHERE p.id IS NULL OR p.role != 'store_owner'
  LIMIT 1
)
RETURNING id, name, owner_id;
*/
