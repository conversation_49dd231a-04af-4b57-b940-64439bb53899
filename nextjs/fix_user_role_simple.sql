-- Simple fix for user role issue
-- Convert <EMAIL> to regular user role

-- Check current role
SELECT 'CURRENT USER ROLE:' as section;
SELECT email, role FROM profiles WHERE email = '<EMAIL>';

-- Update <EMAIL> to regular user role
UPDATE profiles 
SET role = 'user', updated_at = NOW()
WHERE email = '<EMAIL>';

-- Verify the change
SELECT 'UPDATED USER ROLE:' as section;
SELECT email, role FROM profiles WHERE email = '<EMAIL>';

-- Show all role distribution
SELECT 'ALL ROLES DISTRIBUTION:' as section;
SELECT role, COUNT(*) as count
FROM profiles
GROUP BY role
ORDER BY count DESC;
