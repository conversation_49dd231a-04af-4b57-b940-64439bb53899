-- Enhanced Payout System Database Schema
-- This script creates the necessary tables and updates for the enhanced payout system

-- PART 1: UPDATE EXISTING TABLES
-- ===============================

-- Add commission column to payouts table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'payouts' AND column_name = 'commission') THEN
        ALTER TABLE payouts ADD COLUMN commission DECIMAL(10,2) DEFAULT 0.00;
    END IF;
END $$;

-- Add transaction_id column to payouts table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'payouts' AND column_name = 'transaction_id') THEN
        ALTER TABLE payouts ADD COLUMN transaction_id VARCHAR(255);
    END IF;
END $$;

-- Create or update payout_status enum
DO $$
BEGIN
    -- Check if the enum type exists
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'payout_status') THEN
        -- Create the enum type if it doesn't exist
        CREATE TYPE payout_status AS ENUM ('pending', 'processing', 'sent', 'completed', 'failed');
    ELSE
        -- Add 'sent' value if the enum exists but doesn't have 'sent'
        IF NOT EXISTS (
            SELECT 1 FROM pg_enum
            WHERE enumlabel = 'sent'
            AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'payout_status')
        ) THEN
            ALTER TYPE payout_status ADD VALUE 'sent';
        END IF;
    END IF;
EXCEPTION
    WHEN duplicate_object THEN
        -- Value already exists, do nothing
        NULL;
END $$;

-- PART 2: CREATE RECEIPT TRACKING TABLE
-- =====================================

-- Create receipts table to track generated receipts
CREATE TABLE IF NOT EXISTS receipts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    receipt_id VARCHAR(50) UNIQUE NOT NULL, -- Human readable ID like RECEIPT-ABC123
    payout_id UUID NOT NULL REFERENCES payouts(id) ON DELETE CASCADE,
    store_id UUID NOT NULL REFERENCES stores(id) ON DELETE CASCADE,
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    commission DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'GMD',
    transaction_id VARCHAR(255),
    receipt_data JSONB, -- Store detailed receipt information
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for receipts table
CREATE INDEX IF NOT EXISTS idx_receipts_payout_id ON receipts(payout_id);
CREATE INDEX IF NOT EXISTS idx_receipts_store_id ON receipts(store_id);
CREATE INDEX IF NOT EXISTS idx_receipts_order_id ON receipts(order_id);
CREATE INDEX IF NOT EXISTS idx_receipts_generated_at ON receipts(generated_at);

-- PART 3: CREATE FINDER EARNINGS TRACKING
-- =======================================

-- Create finder_earnings table to track commission earnings
CREATE TABLE IF NOT EXISTS finder_earnings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    payout_id UUID REFERENCES payouts(id) ON DELETE SET NULL,
    amount DECIMAL(10,2) NOT NULL, -- Commission amount earned
    currency VARCHAR(3) NOT NULL DEFAULT 'GMD',
    earning_date DATE NOT NULL DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for finder_earnings table
CREATE INDEX IF NOT EXISTS idx_finder_earnings_order_id ON finder_earnings(order_id);
CREATE INDEX IF NOT EXISTS idx_finder_earnings_payout_id ON finder_earnings(payout_id);
CREATE INDEX IF NOT EXISTS idx_finder_earnings_earning_date ON finder_earnings(earning_date);

-- PART 4: UPDATE EXISTING DATA
-- ============================

-- Update existing payouts to have commission data (assuming 5% commission rate)
UPDATE payouts
SET commission = amount * 0.0526 -- 5% commission means payout is 95% of total, so commission = payout * (5/95)
WHERE commission IS NULL OR commission = 0;

-- PART 5: CREATE FUNCTIONS AND TRIGGERS
-- =====================================

-- Function to automatically create finder earnings when payout is created
CREATE OR REPLACE FUNCTION create_finder_earning()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO finder_earnings (order_id, payout_id, amount, currency, earning_date)
    VALUES (NEW.order_id, NEW.id, NEW.commission, NEW.currency, CURRENT_DATE);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to create finder earnings on payout creation
DROP TRIGGER IF EXISTS trigger_create_finder_earning ON payouts;
CREATE TRIGGER trigger_create_finder_earning
    AFTER INSERT ON payouts
    FOR EACH ROW
    EXECUTE FUNCTION create_finder_earning();

-- Function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at columns
DROP TRIGGER IF EXISTS trigger_receipts_updated_at ON receipts;
CREATE TRIGGER trigger_receipts_updated_at
    BEFORE UPDATE ON receipts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_finder_earnings_updated_at ON finder_earnings;
CREATE TRIGGER trigger_finder_earnings_updated_at
    BEFORE UPDATE ON finder_earnings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- PART 6: VERIFICATION QUERIES
-- ============================

-- Check the updated payouts table structure
SELECT 'PAYOUTS TABLE STRUCTURE:' as section;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'payouts'
ORDER BY ordinal_position;

-- Check receipts table structure
SELECT 'RECEIPTS TABLE STRUCTURE:' as section;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'receipts'
ORDER BY ordinal_position;

-- Check finder_earnings table structure
SELECT 'FINDER_EARNINGS TABLE STRUCTURE:' as section;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'finder_earnings'
ORDER BY ordinal_position;

-- Show sample data
SELECT 'SAMPLE PAYOUTS WITH COMMISSION:' as section;
SELECT id, store_id, order_id, amount, commission, currency, payout_status, created_at
FROM payouts
ORDER BY created_at DESC
LIMIT 5;

-- Show total finder earnings
SELECT 'TOTAL FINDER EARNINGS:' as section;
SELECT
    currency,
    SUM(amount) as total_earnings,
    COUNT(*) as total_transactions
FROM finder_earnings
GROUP BY currency
ORDER BY currency;
