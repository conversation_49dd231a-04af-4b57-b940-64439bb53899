-- Update role system to only have admin and user roles
-- This script will convert store_owner roles to admin and update the system

-- PART 1: DIAGNOSIS
-- =================

-- 1. Check current role distribution
SELECT 'CURRENT ROLE DISTRIBUTION:' as section;
SELECT role, COUNT(*) as count
FROM profiles
GROUP BY role
ORDER BY count DESC;

-- 2. Check users with store_owner role
SELECT 'STORE_OWNER USERS:' as section;
SELECT id, email, role, first_name, last_name, created_at
FROM profiles
WHERE role = 'store_owner'
ORDER BY created_at DESC;

-- PART 2: UPDATE ROLES
-- ====================

-- Convert all store_owner roles to admin
-- This assumes that store owners should have admin access to manage the marketplace
UPDATE profiles 
SET role = 'admin', updated_at = NOW()
WHERE role = 'store_owner';

-- If you prefer to convert store_owner to regular user instead, use this:
-- UPDATE profiles 
-- SET role = 'user', updated_at = NOW()
-- WHERE role = 'store_owner';

-- PART 3: VERIFICATION
-- ====================

-- Check updated role distribution
SELECT 'UPDATED ROLE DISTRIBUTION:' as section;
SELECT role, COUNT(*) as count
FROM profiles
GROUP BY role
ORDER BY count DESC;

-- Show all admin users
SELECT 'ADMIN USERS:' as section;
SELECT id, email, role, first_name, last_name, created_at
FROM profiles
WHERE role = 'admin'
ORDER BY created_at DESC;

-- PART 4: UPDATE FEATURE TOGGLES
-- ===============================

-- Update feature toggles to remove store_owner role restrictions
UPDATE feature_toggles 
SET role_restrictions = ARRAY['admin'], updated_at = NOW()
WHERE 'store_owner' = ANY(role_restrictions);

-- Show updated feature toggles
SELECT 'UPDATED FEATURE TOGGLES:' as section;
SELECT feature_key, feature_name, enabled, role_restrictions
FROM feature_toggles
ORDER BY feature_key;

-- PART 5: CLEAN UP STORE OWNERSHIP (OPTIONAL)
-- ============================================

-- If you want to remove store ownership entirely, you can:
-- 1. Set all store owner_id to NULL
-- 2. Or assign all stores to a single admin user

-- Option 1: Remove store ownership (stores become platform-managed)
-- UPDATE stores SET owner_id = NULL, updated_at = NOW();

-- Option 2: Assign all stores to the first admin user
-- UPDATE stores 
-- SET owner_id = (SELECT id FROM profiles WHERE role = 'admin' LIMIT 1), 
--     updated_at = NOW()
-- WHERE owner_id IS NOT NULL;

-- Show stores after update
SELECT 'STORES AFTER UPDATE:' as section;
SELECT s.id, s.name, s.owner_id, p.email as owner_email, s.status
FROM stores s
LEFT JOIN profiles p ON s.owner_id = p.id
ORDER BY s.created_at DESC
LIMIT 10;
